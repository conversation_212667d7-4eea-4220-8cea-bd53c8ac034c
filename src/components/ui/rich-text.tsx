import Link from "next/link";
import Markdown, { Components } from "react-markdown";
import remarkG<PERSON> from "remark-gfm";
import { SyntaxHighlighter, dark } from "react-syntax-highlighter";

export function RichText({ content, className }: { content: string; className?: string }) {
  const components: Components = {
    h1: ({ children }) => <h1 className="text-4xl font-bold">{children}</h1>,
    h2: ({ children }) => <h2 className="text-3xl font-bold">{children}</h2>,
    h3: ({ children }) => <h3 className="text-2xl font-bold">{children}</h3>,
    h4: ({ children }) => <h4 className="text-xl font-bold">{children}</h4>,
    h5: ({ children }) => <h5 className="text-lg font-bold">{children}</h5>,
    h6: ({ children }) => <h6 className="text-lg font-medium">{children}</h6>,
    p: ({ children }) => <p className="text-base">{children}</p>,
    a: ({ children, href }) => {
      if (href?.startsWith("/"))
        return (
          <Link href={href} className="text-primary hover:underline">
            {children}
          </Link>
        );

      return (
        <a
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          className="text-primary hover:underline"
        >
          {children}
        </a>
      );
    },
    img: ({ src, alt }) => <img src={src} alt={alt} className="h-auto max-w-full" />,
    ul: ({ children }) => <ul className="list-inside list-disc">{children}</ul>,
    ol: ({ children }) => <ol className="list-inside list-decimal">{children}</ol>,
    li: ({ children }) => <li className="text-base">{children}</li>,
    blockquote: ({ children }) => (
      <blockquote className="border-primary border-l-4 pl-4">{children}</blockquote>
    ),
    hr: () => <hr className="border-border" />,
    br: () => <br />,
    code: (props) => {
      const { children, className, node, ...rest } = props;
      const match = /language-(\w+)/.exec(className || "");
      return match ? (
        <SyntaxHighlighter
          {...rest}
          PreTag="div"
          children={String(children).replace(/\n$/, "")}
          language={match[1]}
          style={dark}
        />
      ) : (
        <code {...rest} className={className}>
          {children}
        </code>
      );
      return <code className="bg-muted rounded-md p-1 font-mono">{children}</code>;
    },
    pre: ({ children }) => <pre className="bg-muted rounded-md p-4">{children}</pre>,
    b: ({ children }) => <b className="font-bold">{children}</b>,
    em: ({ children }) => <em className="italic">{children}</em>,
    strong: ({ children }) => <strong className="font-bold">{children}</strong>,
    del: ({ children }) => <del className="line-through">{children}</del>,
  };

  return (
    <div className={className}>
      <Markdown components={components} children={content} remarkPlugins={[remarkGFM]} />
    </div>
  );
}
