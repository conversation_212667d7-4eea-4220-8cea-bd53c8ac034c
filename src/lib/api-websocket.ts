import { APIProps } from "@/types/api.type";
import { WEBSOCKET_CONFIG } from "./config-websocket";

export async function apiWebSocket({
  url,
  method,
  headers,
  body,
  cache,
  next,
  apiKey,
}: APIProps & { apiKey: string }) {
  if (!apiKey) {
    throw new Error("Unauthorized");
  }

  const response = await fetch(WEBSOCKET_CONFIG.API + url, {
    method: method ?? "GET",
    headers: {
      "x-api-key": apiKey,
      ...headers,
    },
    body,
    cache: cache ?? "force-cache",
    next,
  });

  if (!response.ok) {
    console.log(await response.json());
    throw new Error(response.statusText);
  }

  return response;
}
