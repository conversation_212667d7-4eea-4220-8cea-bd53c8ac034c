export const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || "http://localhost:1337";

export async function strapi({ path, tags }: { path: string; tags?: string[] }) {
  const BASE_URL = STRAPI_URL;

  return await fetch(BASE_URL + path, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      accept: "application/json",
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    },
    next: {
      revalidate: 60 * 15, // * Revalidate every 15 minutes
      tags,
    },
    cache: "force-cache",
  });
}
