import { <PERSON><PERSON>, MicOff, PhoneOff, Speech, Video, VideoOff } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";

import { useAudioProcessor } from "@/hooks/use-audio-processor";
import { useCamera } from "@/hooks/use-camera";
import { Message } from "@/hooks/use-chat-messages";
import { ConnectionStatus } from "@/hooks/use-websocket";
import { cn } from "@/utils/cn";
import { LoadingBubble, TurnStatus } from "@/components/shared/loading-bubble";
import { ChatMessage } from "../chat-message";
import { AnimatePresence } from "motion/react";
import { useEffect, useRef } from "react";

type UserControlProps = {
  camera: ReturnType<typeof useCamera>;
  audio: ReturnType<typeof useAudioProcessor>;
  className?: string;
  status: ConnectionStatus;
  messages: Message[];
  isLoading: boolean;
  turnStatus: TurnStatus;
  onEndToTalk: () => void;
  endInterview: () => void;
};

export function InterviewSessionUserControlMobile({
  camera,
  audio,
  className,
  status,
  messages,
  isLoading,
  turnStatus,
  onEndToTalk,
  endInterview,
}: UserControlProps) {
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isLoading, turnStatus]);

  return (
    <div
      className={cn(
        "flex w-full flex-col gap-4 bg-gradient-to-t from-black/80 to-transparent p-4",
        className,
      )}
    >
      <div className="flex justify-between gap-6">
        <div className="no-scrollbar mask-gradient-opacity space-y-4 max-h-[400px] w-full gap-4 overflow-auto pt-20">
          <AnimatePresence mode="popLayout">
            {messages.map((msg, idx) => (
              <ChatMessage key={idx + msg.text} message={msg} />
            ))}
            {isLoading && <LoadingBubble turnStatus={turnStatus} />}
          </AnimatePresence>
          <div ref={chatEndRef} />
        </div>

        <div className="flex flex-col gap-6 self-end">
          <Button
            variant={camera.isEnabled ? "outline" : "destructive"}
            size="icon"
            onClick={camera.toggleVideo}
            disabled={status === "connecting"}
            className="size-12 rounded-full md:size-14"
          >
            {camera.isEnabled ? (
              <Video className="size-5 md:size-6" />
            ) : (
              <VideoOff className="size-5 md:size-6" />
            )}
          </Button>

          <Button
            size="icon"
            variant={audio.isRecording ? "outline" : "destructive"}
            onClick={audio.toggleAudio}
            disabled={status === "connecting"}
            className="size-12 rounded-full md:size-14"
          >
            {audio.isRecording ? (
              <Mic className="size-5 md:size-6" />
            ) : (
              <MicOff className="size-5 md:size-6" />
            )}
          </Button>

          <Button
            size="icon"
            variant={"destructive"}
            onClick={endInterview}
            disabled={status === "connecting"}
            className="size-12 rounded-full md:size-14"
          >
            <PhoneOff className="size-5 md:size-6" />
          </Button>
        </div>
      </div>
      <Button className="h-10" disabled={isLoading || turnStatus === "ai"} onClick={onEndToTalk}>
        End To Talk <Speech className="size-4" />
      </Button>
    </div>
  );
}
