import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { endN8N, getN8N } from "../../services/n8n.service";

import { useTimerCountdown } from "@/hooks/use-timer";
import { ConnectionStatus, useWebSocket } from "@/hooks/use-websocket";
import { useCamera } from "@/hooks/use-camera";
import { useAudioProcessor } from "@/hooks/use-audio-processor";
import { useChatMessages } from "@/hooks/use-chat-messages";
import { useUpdateEffect } from "@/hooks/use-update-effect";
import { useDidAvatar } from "@/hooks/use-did-avatar";
import { useSpeechmatics } from "@/hooks/use-speechmatics";
import { WEBSOCKET_CONFIG } from "@/lib/config-websocket";

import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";

import { InterviewSessionHeader } from "./header.interview-session";
import { InterviewSessionCameraUser } from "./camera-user.interview-session";
import { InterviewSessionUserControl } from "./user-control.interview-session";
import { InterviewSessionUserControlMobile } from "./user-control-mobile.interview-session";
import { InterviewSessionChatMessages } from "./chat-messages.interview-session";
import { InterviewSessionCameraAI } from "./camera-ai.interview-session";

interface SpeechmaticsMessage {
  type: "transcript" | "end_of_utterance";
  transcript?: string;
}

interface InterviewSessionProps {
  sessionID: string;
  isMobile: boolean;
  onEndInterview: () => void;
}

interface N8NPayload {
  type: string;
  payload: { text: string } | null;
}

type DeviceType = {
  deviceId: string;
  label: string;
  groupId: string;
};

type N8NChatMessage =
  | { type: "new_question"; payload: { question: string } }
  | { type: "user_answer"; payload: { answer: string } };

export function InterviewSession({ sessionID, isMobile }: InterviewSessionProps) {
  const warnTime = "00:15";
  const n8nUrl = useMemo(() => `${WEBSOCKET_CONFIG.WSS}/ws/interview/${sessionID}/`, [sessionID]);

  // Combine related states to reduce re-renders
  const [connectionState, setConnectionState] = useState({
    status: "disconnected" as ConnectionStatus,
    statusText: "",
    hasStarted: false,
  });

  const [interviewState, setInterviewState] = useState({
    turnStatus: "ai" as "ai" | "user",
    isStart: false,
    isLoadingChat: false,
    isEndToTalk: false,
  });

  const messagesRef = useRef<string[]>([]);

  // Create a ref to always have the latest interviewState
  const interviewStateRef = useRef(interviewState);
  const speechMaticIsConnectedRef = useRef(false);
  interviewStateRef.current = interviewState;

  const [isDialogOpen, setIsDialogOpen] = useState(true);
  const [devices, setDevices] = useState<DeviceType[]>([]);
  const [n8nMessage, setN8nMessage] = useState("");

  const session = useSession();
  const chat = useChatMessages();
  const timer = useTimerCountdown(10, warnTime);
  const camera = useCamera();
  const router = useRouter();

  const apiKey = useMemo(() => session.data?.user.service_api_key || "", [session.data]);

  const didCallbacks = useMemo(
    () => ({
      onOpen: () => {
        n8n.connect();
      },
      onDataChannel: (event: string) => {
        if (event === "stream/done") {
          setInterviewState((prev) => ({ ...prev, turnStatus: "user" }));
        }
      },
    }),
    [],
  );

  const did = useDidAvatar(didCallbacks);

  const n8nCallbacks = useMemo(
    () => ({
      onOpen: async () => {
        setConnectionState((prev) => ({
          ...prev,
          status: "connected",
          statusText: "connected to AI",
        }));
        await getN8N(sessionID, apiKey);
        console.log("N8N CONNECTED");
      },
      onClose: async () => {
        await endN8N(sessionID, apiKey);
      },
      onMessage: (data: N8NPayload) => {
        if (data.type === "end_interview") {
          console.log("End of Interview", data);
        }

        setInterviewState((prev) => ({ ...prev, isLoadingChat: true, turnStatus: "ai" }));
        if (data.payload?.text !== undefined) {
          setN8nMessage(data.payload.text);
          chat.addMessage({
            type: "ai",
            text: data.payload?.text || "",
            timestamp: new Date(),
          });

          setInterviewState((prev) => ({ ...prev, isLoadingChat: false }));
        } else {
          setInterviewState((prev) => ({ ...prev }));
        }
      },
    }),
    [sessionID, apiKey, chat],
  );

  const n8n = useWebSocket<N8NPayload, N8NChatMessage>(n8nUrl, n8nCallbacks);

  const speechmaticsCallbacks = useMemo(
    () => ({
      onOpen: () => {
        console.log("Speechmatics connected");
        speechMaticIsConnectedRef.current = true;
      },
      onMessage: (data: SpeechmaticsMessage) => {
        if (data.type === "transcript" && data.transcript) {
          chat.addStreamMessage(data.transcript, "user");
          messagesRef.current.push(data.transcript);
        }
      },
      onClose: () => {
        console.log("Speechmatics closed");
      },
      onError: () => {
        console.error("Speechmatics error");
      },
      onEndOfUtterance: () => {
        console.log("End of utterance");
        handleOnEndToTalk(messagesRef.current);
      },
    }),
    [],
  );

  const speechmatics = useSpeechmatics(speechmaticsCallbacks);

  const handleAudioCallback = useMemo(
    () => ({
      onOpen: () => {},
      onMessage: (data: Int16Array) => {
        if (interviewStateRef.current.turnStatus === "user" && speechMaticIsConnectedRef.current) {
          speechmatics.send(data);
        }
      },
      onError: (error: Error) => {
        console.error("Audio processor error:", error);
      },
    }),
    [speechmatics.isConnected, speechmatics.send],
  );

  const audio = useAudioProcessor(handleAudioCallback);

  // Memoized start interview handler
  const handleStartInterview = useCallback(async () => {
    setIsDialogOpen(false);
    setConnectionState((prev) => ({
      ...prev,
      status: "connecting",
      statusText: "connecting to AI",
      hasStarted: true,
    }));

    await did.connect();
    await audio.connect();
    await camera.connect();
    await speechmatics.connect();

    timer.startTimer();
  }, []);

  const handleOnEndToTalk = useCallback(
    (messages: string[]) => {
      chat.addStreamMessage("", "user");
      const payload: N8NChatMessage = {
        type: "user_answer",
        payload: { answer: messages.join(" ") },
      };

      n8n.sendMessage(payload);

      setInterviewState((prev) => ({ ...prev, isEndToTalk: true, turnStatus: "ai" }));
      messagesRef.current = [];
    },
    [chat, n8n.sendMessage],
  );

  const handleEndInterview = useCallback(async () => {
    n8n.disconnect();
    did.disconnect();
    speechmatics.disconnect();
    toast("Interview ended", {
      description: "Hasil interview akan diupload ke dashboard",
      duration: 5000,
    });
    router.push("/dashboard");
  }, [n8n.disconnect, did.disconnect, speechmatics.disconnect, toast, router]);

  useUpdateEffect(() => {
    // if (
    //   n8n.lastMessage?.payload?.text &&
    //   did.eventLabel === "stream/done" &&
    //   interviewState.turnStatus === "ai"
    // ) {
    //   did.sendStreamMessage(n8n.lastMessage.payload?.text || "");
    //   return;
    // }

    if (n8n.lastMessage?.payload?.text && did.eventLabel === "stream/ready") {
      did.sendStreamMessage(n8n.lastMessage.payload.text);
      setInterviewState((prev) => ({ ...prev, isStart: true }));
      return;
    }
  }, [did.eventLabel, n8n.lastMessage, interviewState.turnStatus]);

  useUpdateEffect(() => {
    if (!interviewStateRef.current.isStart) return;

    if (n8nMessage) {
      did.sendStreamMessage(n8nMessage);
      return;
    }
  }, [n8nMessage]);

  useUpdateEffect(() => {
    if (did.eventLabel === "chat/answer" && interviewState.isEndToTalk) {
      setInterviewState((prev) => ({ ...prev, isEndToTalk: false }));
      return;
    }
  }, [did.eventLabel, interviewState.isEndToTalk]);

  useUpdateEffect(() => {
    if (n8n.lastMessage?.type === "end_interview") {
      handleEndInterview();
    }
  }, [n8n.lastMessage?.type]);

  // useEffect(() => {
  //   timeoutRef.current = setTimeout(() => {
  //     if (interviewState.turnStatus === "user" && message.length > 0) {
  //       handleOnEndToTalk([...message]);
  //     }
  //   }, 3000);

  //   return () => {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //       timeoutRef.current = null;
  //     }
  //   };
  // }, [interviewState.turnStatus, message.length]);

  // useEffect(() => {
  //   // Clear timeout when messages change to restart the timer
  //   if (timeoutRef.current && message.length === 0) {
  //     clearTimeout(timeoutRef.current);
  //   }
  // }, [message.length]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup all connections
      messagesRef.current = [];
      n8n.disconnect?.();
      did.disconnect?.();
      speechmatics.disconnect?.();
      audio.stop?.();
    };
  }, []);

  useEffect(() => {
    let mounted = true;

    const initAudioDevices = async () => {
      try {
        const deviceEnum = await audio.getAudioDevices();
        if (mounted) {
          setDevices(deviceEnum);
        }
      } catch (error) {
        console.error("Failed to enumerate audio devices:", error);
      }
    };

    initAudioDevices();

    return () => {
      mounted = false;
    };
  }, [audio]);

  useEffect(() => {
    if (connectionState.status === "connecting") {
      setInterviewState((prev) => ({ ...prev, isLoadingChat: true }));
    }
  }, [connectionState.status]);

  useEffect(() => {
    const handleBeforeUnload = (e: Event) => {
      e.preventDefault();
      // @ts-expect-error deprecated returnValue
      e.returnValue = "";
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  return (
    <div className="relative flex size-full flex-col">
      {connectionState.status === "connected" && (
        <div className="absolute top-20 left-4 z-20 flex h-9 items-center justify-center rounded-full bg-black/10 px-4 py-2 text-white backdrop-blur-xs md:top-24 md:left-1/2 md:-translate-x-1/2">
          Turn: {interviewState.turnStatus.toUpperCase()}
        </div>
      )}

      <Dialog open={isDialogOpen}>
        <DialogContent showCloseButton={false}>
          <DialogHeader>
            <DialogTitle>Start Interview</DialogTitle>
            <DialogDescription>Apakah Anda siap untuk memulai interview?</DialogDescription>
          </DialogHeader>

          <div className="flex flex-col justify-between gap-4">
            {devices.length > 0 && (
              <Select onValueChange={audio.switchDevice}>
                <SelectTrigger disabled={audio.isLoading || devices.length <= 1} className="w-full">
                  {devices.find((device) => device.deviceId === audio.currentDeviceId)?.label ||
                    "Pilih Microphone"}
                  {audio.isLoading && <Loader2 className="size-4 shrink-0 animate-spin" />}
                </SelectTrigger>

                <SelectContent>
                  {devices.map((device) => (
                    <SelectItem key={device.deviceId} value={device.deviceId}>
                      {device.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            <Button onClick={handleStartInterview}>Start Interview</Button>
          </div>
        </DialogContent>
      </Dialog>

      <InterviewSessionHeader
        // title="bigo-live"
        status={connectionState.status}
        timeLeft={timer.timeLeft}
        isAlmostFinished={timer.isAlmostFinished}
        connectionStatus={connectionState.statusText}
      />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0 pr-0 pb-0 pl-0 md:flex-row md:pt-4 md:pl-4">
        <div className="flex w-full flex-1 flex-col gap-4 pb-0">
          <div className="flex w-full flex-1 flex-col gap-4">
            <Card className="relative h-full w-full overflow-hidden rounded-none border-0 p-0 shadow-none md:rounded-xl md:border">
              <InterviewSessionCameraAI
                idleVideoRef={did.idleVideoRef}
                streamVideoRef={did.streamVideoRef}
                isLoading={did.isLoading}
              />
              <InterviewSessionCameraUser
                className="absolute left-0 m-4 w-[140px] shrink-0 max-md:top-0 max-md:aspect-[3/4] md:bottom-0 md:w-xs"
                isMobile={isMobile}
                camera={camera}
              />
              <InterviewSessionUserControlMobile
                className="absolute right-0 bottom-0 z-50 md:hidden"
                camera={camera}
                audio={audio}
                messages={chat.messages}
                status={connectionState.status}
                isLoading={interviewState.isLoadingChat}
                turnStatus={interviewState.turnStatus}
                onEndToTalk={() => handleOnEndToTalk(messagesRef.current)}
                endInterview={handleEndInterview}
              />
            </Card>
          </div>
          <InterviewSessionUserControl
            className="hidden md:block"
            camera={camera}
            audio={audio}
            status={connectionState.status}
            endInterview={handleEndInterview}
          />
        </div>

        <InterviewSessionChatMessages
          messages={chat.messages}
          isLoading={interviewState.isLoadingChat}
          turnStatus={interviewState.turnStatus}
          onEndToTalk={() => handleOnEndToTalk(messagesRef.current)}
          className="hidden md:block"
        />
      </div>
    </div>
  );
}
