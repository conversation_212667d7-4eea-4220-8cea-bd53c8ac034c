"use client";

import { useState } from "react";
import { Copy, Download, Mail, Share2 } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { IInterviewResult } from "@/types/response.type";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

type InterviewSessionHeaderProps = {
  id: string;
  data: IInterviewResult;
};

export function InterviewSessionHeader({ id, data }: InterviewSessionHeaderProps) {
  const [shareDropdownOpen, setShareDropdownOpen] = useState(false);

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success("Link copied to clipboard!", {
        description: "You can share this link with your team",
      });
    } catch (err) {
      console.error("Failed to copy link:", err);
    }
    setShareDropdownOpen(false);
  };

  const handleEmailShare = () => {
    const subject = `Interview Results - ${data.interview.posisi} at ${data.interview.nama_perusahaan}`;
    const body = `Interview results for ${data.user.email}\nBooking Code: ${data.interview.booking_code}\nFinal Score: ${data.result.final_score}/100\n\nView full results: ${window.location.href}`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
    setShareDropdownOpen(false);
  };

  const handleDownload = () => {
    window.print();
    setShareDropdownOpen(false);
  };

  return (
    <div className="bg-background space-y-6 border-b p-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Interview Results</h1>
          <p className="text-muted-foreground">Booking Code: {id}</p>
        </div>

        <div className="flex items-center gap-3 self-start">
          <Badge>{data.interview.status}</Badge>

          <DropdownMenu open={shareDropdownOpen} onOpenChange={setShareDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                aria-label="Share"
                aria-expanded={shareDropdownOpen}
                aria-controls="share-dropdown"
                title="Share"
                className="flex size-6 items-center bg-transparent"
              >
                <Share2 className="size-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleCopyLink} className="flex items-center gap-2">
                <Copy className="h-4 w-4" />
                <span className="leading-none">Copy Link</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEmailShare} className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="leading-none">Share via Email</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDownload} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                <span className="leading-none">Download or Print</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
