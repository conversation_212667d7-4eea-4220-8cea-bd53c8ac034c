import { RiPlayFill } from "@remixicon/react";
import { VariantProps } from "class-variance-authority";
import {
  <PERSON><PERSON><PERSON>,
  BadgeCheck,
  Building2,
  <PERSON><PERSON><PERSON>ck2,
  <PERSON>O<PERSON>,
  History,
} from "lucide-react";
import Link from "next/link";

import { Badge, badgeVariants } from "@/components/ui/badge";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  Card,
  CardAction,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { formatDate } from "@/lib/format-date";
import { IInterview } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { Separator } from "@/components/ui/separator";

type CardSessionProps = {
  showAll?: boolean;
  sessions?: IInterview[] | null;
};

export function CardSession({ showAll = false, sessions = [] }: CardSessionProps) {
  if (sessions?.length) {
    sessions?.sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
  }

  return (
    <Card className="shadow-none">
      <CardHeader className="border-b">
        <CardTitle className="text-xl">Riwayat Interview</CardTitle>
        {showAll && (
          <CardAction>
            <Button asChild variant="link" className="text-purple-700">
              <Link href={"#"}>
                Lihat Semua <ArrowRight />
              </Link>
            </Button>
          </CardAction>
        )}
      </CardHeader>

      {sessions?.length === 0 ? (
        <CardFallback />
      ) : (
        <CardContent className="no-scrollbar h-[450px] overflow-auto">
          <div className="pointer-events-none sticky top-0 w-full">
            <div className="from-card absolute top-0 left-0 z-20 h-10 w-full bg-gradient-to-b to-transparent"></div>
          </div>
          <ul className="w-full flex-1 shrink-0 space-y-4 py-6">
            {sessions?.map((item, idx) => {
              const badgeVariant: Record<
                IInterview["status"],
                VariantProps<typeof badgeVariants>["variant"]
              > = {
                Pending: "pending",
                Scheduled: "secondary",
                Completed: "success",
                Cancelled: "destructive",
              };

              const badgeLevelVariant: Record<IInterview["tingkatan"], string> = {
                Entry: "bg-green-50 text-green-500 border-green-100",
                Mid: "bg-green-100 text-green-600 border-green-200",
                Senior: "bg-green-200 text-green-700 border-green-300",
                Lead: "bg-green-300 text-green-800 border-green-400",
                Manager: "bg-green-400 text-green-900 border-green-500",
              };

              const buttonVariant: Record<
                IInterview["status"],
                VariantProps<typeof buttonVariants>["variant"]
              > = {
                Cancelled: "destructive",
                Pending: "secondary",
                Scheduled: "secondary",
                Completed: "default",
              };

              const buttonLabel: Record<IInterview["status"], string> = {
                Cancelled: "Cancelled",
                Pending: "Evaluating",
                Scheduled: "Mulai Interview",
                Completed: "Lihat Hasil",
              };

              const iconVariants: Record<Lowercase<IInterview["status"]>, React.ReactNode> = {
                cancelled: <CircleOff className="size-2/5" />,
                pending: <History className="size-2/5" />,
                scheduled: <CalendarCheck2 className="size-2/5" />,
                completed: <BadgeCheck className="size-2/5" />,
              };

              const Icon = () =>
                iconVariants[item.status.toLowerCase() as keyof typeof iconVariants] || (
                  <BadgeCheck className="size-2/5" />
                );

              return (
                <li key={idx} className="grid min-w-[700px] grid-cols-3 place-content-center gap-4">
                  <div className="flex items-center gap-4">
                    <div className="bg-primary-foreground border-primary/20 text-primary flex size-14 shrink-0 items-center justify-center rounded-xl border">
                      <Icon />
                    </div>

                    <div className="truncate">
                      <p className="truncate font-sans font-medium">{item.posisi}</p>
                      <p className="text-muted-foreground text-sm">{formatDate(item.date)}</p>
                      <Badge variant={badgeVariant[item.status]}>{item.status}</Badge>
                    </div>
                  </div>

                  <div className="text-xs">
                    <p className="">Booking Code</p>
                    <p className="font-medium">{item.booking_code}</p>
                  </div>

                  <div className="ml-auto flex items-center gap-6">
                    <div className="flex flex-col items-center justify-center">
                      <div className={cn("clamp-[text,lg,xl] font-bold")}>
                        {!item.final_score ? "-" : item.final_score}
                      </div>
                      <div className="text-muted-foreground text-xs">Skor</div>
                    </div>

                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          disabled={item.status === "Pending"}
                          size="sm"
                          variant={buttonVariant[item.status]}
                        >
                          {buttonLabel[item.status] || "Lihat Hasil"}
                        </Button>
                      </DialogTrigger>

                      <DialogContent className="font-sans">
                        <DialogHeader className="flex flex-row">
                          <div className="w-full space-y-2">
                            <div className="flex items-center gap-4">
                              <DialogTitle className="leading-none">Hasil Interview</DialogTitle>
                              <Badge
                                className={cn(
                                  "size-fit self-end border",
                                  badgeLevelVariant[item.tingkatan],
                                )}
                              >
                                {item.tingkatan}
                              </Badge>
                            </div>
                            <DialogDescription className="flex items-center gap-2">
                              <Building2 className="text-foreground size-3" />
                              <span className="text-foreground">
                                {item.nama_perusahaan || "Not specified"}
                              </span>{" "}
                              -{" "}
                              <span className="uppercase">{item.industri || "Not specified"}</span>
                            </DialogDescription>
                          </div>
                        </DialogHeader>

                        <div className="space-y-3">
                          <p className="text-sm">{item.summary}</p>

                          <Separator />

                          <div>
                            <h4 className="mb-1 font-semibold text-green-600">Key Strengths</h4>
                            <p className="text-muted-foreground text-sm">{item.kekuatan}</p>
                          </div>

                          <div>
                            <h4 className="mb-1 font-semibold text-orange-600">
                              Development Areas
                            </h4>
                            <p className="text-muted-foreground text-sm">{item.kelemahan}</p>
                          </div>
                        </div>

                        <div className="mt-4 flex flex-col items-center justify-between gap-4 md:flex-row">
                          <div className="flex items-center gap-2">
                            <div className="bg-muted flex justify-center gap-1 rounded-md border p-2">
                              <p className="text-muted-foreground text-xs leading-none">Score</p>
                              <p className="text-xs leading-none font-semibold">
                                {item.final_score?.toString() || "Calibrate"}
                              </p>
                            </div>
                          </div>

                          <Button asChild size="sm" disabled={item.status === "Pending"}>
                            <Link
                              href={
                                item.status === "Scheduled"
                                  ? `/dashboard/interview/${item.booking_code}`
                                  : `/dashboard/interview/${item.booking_code}/detail`
                              }
                              autoFocus
                            >
                              {buttonLabel[item.status] || "Lihat Hasil"}
                            </Link>
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </li>
              );
            })}
          </ul>
          <div className="pointer-events-none sticky bottom-0 w-full">
            <div className="from-card absolute bottom-0 left-0 z-20 h-10 w-full bg-gradient-to-t to-transparent"></div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

function CardFallback() {
  return (
    <>
      <CardContent className="flex flex-col items-center pt-4">
        <div className="mb-4 flex items-center justify-center">
          <div className="bg-muted text-muted-foreground flex size-20 items-center justify-center rounded-full">
            <History className="size-7" />
          </div>
        </div>

        <div className="space-y-2 text-center">
          <p className="text-base leading-none font-semibold md:text-lg">
            Belum Ada Riwayat Interview
          </p>
          <p className="text-muted-foreground text-sm">
            Anda belum memiliki riwayat interview. Mulai sesi pertama Anda sekarang!
          </p>
        </div>
      </CardContent>

      <CardFooter className="justify-center pb-4">
        <Button asChild className="bg-gradient-purple hover:opacity-80">
          <Link href="/dashboard/interview/create">
            <RiPlayFill /> Mulai Interview Pertama
          </Link>
        </Button>
      </CardFooter>
    </>
  );
}
