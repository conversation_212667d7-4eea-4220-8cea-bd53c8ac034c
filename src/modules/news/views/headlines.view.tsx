import { FileX2 } from "lucide-react";
import { StrapiNewsHeadlineSection } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

import HeadlinesCard from "../components/headlines-card";

const HeadlinesNewsView = ({ data }: { data: StrapiNewsHeadlineSection }) => {
  return (
    <section className="px-4 py-16 md:px-6">
      <div className="mb-8 text-center">
        <h2 className="text-3xl font-bold text-black">{data.title}</h2>
        <p className="text-[#4B5563]">{data.description}</p>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        {data.latest_article ? (
          <HeadlinesCard
            variant="terbaru"
            date={new Date(data.latest_article.publishedAt).toLocaleDateString("id-ID", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })}
            title={data.latest_article.title}
            description={data.latest_article.content.slice(0, 150) + "..."}
            buttonText="Baca Selengkapnya"
            href={`/news/${data.latest_article.slug}`}
            imageUrl={getImageUrl(data.latest_article.thumbnail?.url || "", "strapi")}
            isFeatured
            className="md:col-span-2 md:row-span-2"
          />
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
            <FileX2 className="text-muted-foreground size-12" />
            <p className="text-muted-foreground text-sm">No latest article</p>
          </div>
        )}

        {data.articles ? (
          <div className="col-span-2 space-y-4">
            {data.articles.map((article) => {
              return (
                <HeadlinesCard
                  key={article.id}
                  variant={
                    article.news_category.badgeText.toLowerCase() as
                      | "update"
                      | "tips"
                      | "komunitas"
                      | "terbaru"
                  }
                  date={new Date(article.publishedAt).toLocaleDateString("id-ID", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}
                  title={article.title}
                  description={article.content.slice(0, 100) + "..."}
                  buttonText="Baca"
                  imageUrl={getImageUrl(article.thumbnail?.url || "", "strapi")}
                  href={`/news/${article.slug}`}
                />
              );
            })}
          </div>
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
            <FileX2 className="text-muted-foreground size-12" />
            <p className="text-muted-foreground text-sm">No article</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default HeadlinesNewsView;
