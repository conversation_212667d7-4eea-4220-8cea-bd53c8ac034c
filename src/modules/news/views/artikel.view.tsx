"use client";

import { useMemo, useState } from "react";
import { FileX2 } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";

import { StrapiNewsArticleSection } from "@/types/strapi.type";
import { STRAPI_URL } from "@/lib/strapi";

import ArticleCard from "../components/article-card";

const ArticleNewsView = ({ data: articleSection }: { data: StrapiNewsArticleSection }) => {
  const [selected, setSelected] = useState("all");

  useMemo(() => {
    if (selected === "all") return articleSection.articles || [];

    return (articleSection.articles || []).filter(
      (a) => a.news_category.badgeText.toLowerCase() === selected,
    );
  }, [selected, articleSection.articles]);

  return (
    <section className="bg-[#E5E7EB] px-6 py-14">
      <div className="mx-auto w-full">
        <h2 className="mb-2 text-center text-3xl font-bold text-black">{articleSection.title}</h2>
        <p className="mb-7 text-center text-[#4B5563]">{articleSection.description}</p>

        <div className="mb-10 flex flex-wrap justify-center gap-3">
          {articleSection.categories ? (
            articleSection.categories.map((cat) => (
              <Button
                key={cat.id}
                variant={selected === cat.name ? "default" : "outline"}
                className={
                  selected === cat.name
                    ? "rounded-full bg-[#7C3AED] font-semibold text-white"
                    : "rounded-full border border-[#E5E7EB] bg-white text-[#6B7280]"
                }
                onClick={() => setSelected(cat.name)}
              >
                {cat.name}
              </Button>
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
              <FileX2 className="text-muted-foreground size-12" />
              <p className="text-muted-foreground text-sm">No categories</p>
            </div>
          )}
        </div>

        <div className="grid grid-cols-3 gap-6">
          {articleSection.articles ? (
            articleSection.articles.map((item) => (
              <ArticleCard
                key={item.id}
                id={item.id}
                image={`${STRAPI_URL}${item.thumbnail?.url}`}
                title={item.title}
                desc={item.content}
                date={new Date(item.publishedAt).toLocaleDateString("id-ID", {
                  day: "numeric",
                  month: "short",
                  year: "numeric",
                })}
                badge={{
                  text: item.news_category.badgeText,
                  color: item.news_category.badgeStyle,
                }}
                link={`/news/${item.slug}`}
              />
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
              <FileX2 className="text-muted-foreground size-12" />
              <p className="text-muted-foreground text-sm">No articles</p>
            </div>
          )}
        </div>

        {articleSection.articles && articleSection.articles.length > 4 && (
          <div className="mt-10 flex justify-center">
            <Button
              className="rounded-xl bg-[#7C3AED] px-7 py-6 text-base font-semibold text-white shadow transition hover:bg-[#7C3AED]"
              asChild
            >
              <Link href="#">Muat Lebih Banyak</Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default ArticleNewsView;
