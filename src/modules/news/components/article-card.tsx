"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";
import { FiEye } from "react-icons/fi";

interface ArticleCardProps {
  id: number;
  image: string;
  title: string;
  desc: string;
  date: string;
  badge: { text: string; color: string };
  link: string;
}

const ArticleCard = ({ image, title, desc, date, badge, link }: ArticleCardProps) => {
  return (
    <Card className="overflow-hidden rounded-xl border border-[#E5E7EB] bg-white pt-0 pb-0 shadow-sm transition-shadow hover:shadow-lg">
      <div className="relative h-48 w-full">
        <Image
          src={image}
          alt={title}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, 33vw"
          priority
        />
      </div>
      <CardContent className="p-5">
        <div className="mb-3 flex items-center gap-3">
          <Badge className={`${badge.color} rounded-full px-2 py-1 text-xs font-semibold`}>
            {badge.text}
          </Badge>
          <span className="text-xs text-[#64748B]">{date}</span>
        </div>
        <h3 className="mb-2 line-clamp-2 text-base leading-snug font-semibold text-[#18181B]">
          {title}
        </h3>
        <p className="mb-4 line-clamp-3 min-h-[48px] text-sm text-[#64748B]">{desc}</p>
        <div className="flex items-center justify-between">
          <Link href={link} className="p-0 text-left text-purple-600 hover:underline">
            <span>Baca</span>
            <span className="block">&rarr;</span>
          </Link>
          <div className="flex items-center gap-1 text-xs text-[#A1A1AA]">
            <FiEye className="h-4 w-4" />
            2K
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ArticleCard;
