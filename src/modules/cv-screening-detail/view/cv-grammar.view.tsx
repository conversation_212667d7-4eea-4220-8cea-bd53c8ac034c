import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card";
import { ICVScreeningReportResponse } from "@/types/response.type";

export function CVGrammar({ data }: { data: ICVScreeningReportResponse }) {
  return (
    <Card className="max-h-[400px] overflow-hidden shadow-none">
      <CardHeader className="border-b">
        <h3 className="text-lg font-bold">Analisis Grammar CV</h3>
        <CardDescription>
          Berikut adalah daftar kesalahan grammar yang ditemukan dalam CV Anda
        </CardDescription>
      </CardHeader>

      <CardContent className="no-scrollbar overflow-auto">
        <ul className="space-y-4">
          {data.grammar.map((item, index) => (
            <li
              key={index}
              className="flex flex-col gap-2.5 rounded-xl border p-4 text-sm md:text-base"
            >
              <h4 className="block leading-none">{item.label}</h4>
              <p className="text-muted-foreground">{item.detail}</p>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <p className="text-xs ml-auto">Total {data.grammar.length} kesalahan grammar</p>
      </CardFooter>
    </Card>
  );
}
