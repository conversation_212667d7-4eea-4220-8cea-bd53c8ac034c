@import "tailwindcss";
@import "tw-animate-css";
/* testingg */
@plugin "tailwind-clamp";

@import "./utility.css";
@import "./variables.css";

@custom-variant dark (&:is(:root));

@theme inline {
  --breakpoint-3xl: 1600px;
  --breakpoint-4xl: 2000px;
  --color-selection: var(--selection);
  --color-selection-foreground: var(--selection-foreground);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-inter: var(--font-inter);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --aspect-default: 40/21;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  ::selection {
    @apply bg-selection text-selection-foreground;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
  }

  @supports (font: -apple-system-body) and (-webkit-appearance: none) {
    [data-wrapper] {
      @apply min-[1800px]:border-t;
    }
  }

  a,
  button {
    @apply cursor-pointer;
  }

  a:active,
  button:active {
    @apply opacity-60 md:opacity-100;
  }
}

@utility bg-gradient-purple {
  background-image: linear-gradient(90deg, rgba(147, 51, 234, 1) 0%, rgba(219, 39, 119, 1) 100%);
}

@utility bg-gradient-green {
  background-image: linear-gradient(90deg, rgba(34, 197, 94, 1) 0%, rgba(59, 130, 246, 1) 100%);
}
