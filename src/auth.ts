import NextAuth from "next-auth";
import authConfig from "../auth.config";
import { googleSession } from "./modules/auth/services/login.service";

export const {
  handlers,
  signIn,
  signOut,
  auth,
  unstable_update: update,
} = NextAuth({
  trustHost: true,
  secret: process.env.AUTH_SECRET,
  pages: { signIn: "/login" },
  session: { strategy: "jwt" },
  callbacks: {
    redirect() {
      return "/dashboard";
    },
    async jwt({ token, user, account, trigger, session }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.username = user.username;
        token.phone_number = user.phone_number;
        token.date_of_birth = user.date_of_birth;
        token.image = user.image;
        token.gender = user.gender;
        token.user = user.user;
        token.bio = user.bio;
        token.service_api_key = user.service_api_key;
        token.isGoogleAuth = false;
      }

      if (account && account.provider === "google") {
        const res = await googleSession(account.access_token || "");

        token.accessToken = account.access_token;
        token.id = res?.id ? res.id : user?.id;
        token.email = user?.email;
        token.username = user?.username;
        token.name = res?.full_name ? res.full_name : user?.name;
        token.phone_number = res?.phone_number ? res.phone_number : user?.phone_number;
        token.date_of_birth = res?.date_of_birth ? res.date_of_birth : user?.date_of_birth;
        token.gender = res?.gender ? res.gender : user?.gender;
        token.user = res?.user ? res.user : user?.user;
        token.bio = res?.bio ? res.bio : user?.bio;
        token.image = res?.profile_picture ? res.profile_picture : user?.image;
        token.profile_picture = res?.profile_picture;
        token.service_api_key = res?.service_api_key ? res.service_api_key : user?.service_api_key;
        token.isGoogleAuth = true;

        return { ...token, accessToken: token.accessToken };
      }

      if (trigger === "update") {
        token.name = session.name;
        token.phone_number = session.phone_number;
        token.date_of_birth = session.date_of_birth;
        token.gender = session.gender;
        token.bio = session.bio;
        token.image = session.image;
      }

      return token;
    },
    session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.name = (token.full_name || token.name) as string;
        session.user.email = token.email as string;
        session.user.username = token.username as string;
        session.user.phone_number = token.phone_number as string;
        session.user.date_of_birth = token.date_of_birth as string;
        session.user.image = token.image as string;
        session.user.gender = token.gender as string;
        session.user.user = token.user as string;
        session.user.bio = token.bio as string;
        session.user.service_api_key = token.service_api_key as string;
        session.user.isGoogleAuth = token.isGoogleAuth as boolean;
      }
      session.sessionToken = token.accessToken as string;
      return session;
    },
  },
  ...authConfig,
});
