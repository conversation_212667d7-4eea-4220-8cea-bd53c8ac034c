import { Users } from "lucide-react";
import { Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>hatsapp } from "react-icons/fa";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog";

import { MainHeaderNavigation } from "@/components/navigation/main-navigation/header";
import { MainFooterNavigation } from "@/components/navigation/main-navigation/footer";

const link = {
  discord: "https://discord.gg/your-server",
  whatsapp: "https://chat.whatsapp.com/your-group",
};

export default function MainLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="relative z-10 flex min-h-svh flex-col">
      <MainHeaderNavigation />

      <main className="flex flex-1 flex-col">{children}</main>

      <MainFooterNavigation />

      <Dialog defaultOpen={true}>
        <DialogContent className="border-0 p-0 sm:max-w-md" closeButtonClassName="[&_svg]:text-white">
          <Card className="border-0 pt-0 shadow-none">
            <CardContent className="p-0">
              {/* Header with close button */}
              <div className="relative rounded-t-lg bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
                <div className="text-center">
                  <div className="mb-3 flex justify-center">
                    <div className="rounded-full bg-white/20 p-3">
                      <Users className="h-8 w-8" />
                    </div>
                  </div>
                  <DialogTitle className="mb-2 text-2xl font-bold">
                    Bergabung dengan Komunitas Kami!
                  </DialogTitle>
                  <DialogDescription className="text-white/90">
                    Dapatkan update terbaru, tips, dan diskusi menarik dengan bergabung di platform
                    favorit Anda
                  </DialogDescription>
                </div>
              </div>

              {/* Content */}
              <div className="space-y-4 p-6">
                {/* Discord Section */}
                <div className="flex items-center gap-4 rounded-lg border border-[#5865F2]/20 bg-[#5865F2]/10 p-4">
                  <div className="rounded-lg bg-[#5865F2] p-3">
                    <FaDiscord className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="mb-1 font-semibold text-[#5865F2]">Discord</h3>
                    <p className="text-muted-foreground text-sm">
                      Chat real-time, voice channel, dan komunitas aktif 24/7
                    </p>
                  </div>
                  <Button asChild className="bg-primary hover:bg-primary text-white">
                    <Link href={link.discord} target="_blank">
                      Join Discord
                    </Link>
                  </Button>
                </div>

                {/* WhatsApp Section */}
                <div className="flex items-center gap-4 rounded-lg border border-[#25D366]/20 bg-[#25D366]/10 p-4">
                  <div className="rounded-lg bg-[#25D366] p-3">
                    <FaWhatsapp className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="mb-1 font-semibold text-[#25D366]">WhatsApp</h3>
                    <p className="text-muted-foreground text-sm">
                      Grup diskusi, update cepat, dan notifikasi penting
                    </p>
                  </div>
                  <Button asChild className="bg-[#25D366] text-white hover:bg-[#1DA851]">
                    <Link href={link.whatsapp} target="_blank">
                      Join WhatsApp
                    </Link>
                  </Button>
                </div>

                {/* Benefits */}
                <div className="bg-muted/50 mt-6 rounded-lg p-4">
                  <h4 className="mb-3 font-medium">Keuntungan Bergabung:</h4>
                  <ul className="text-muted-foreground space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                      Update dan pengumuman terbaru
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                      Diskusi dengan member lain
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                      Tips dan trik eksklusif
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                      Support dan bantuan langsung
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <DialogClose>Mungkin Nanti</DialogClose>
              </Button>
            </CardFooter>
          </Card>
        </DialogContent>
      </Dialog>
    </div>
  );
}
