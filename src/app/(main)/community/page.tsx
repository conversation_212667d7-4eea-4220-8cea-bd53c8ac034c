import { strapi } from "@/lib/strapi";
import DiscordServerCommunityView from "@/modules/community/view/discord.view";
import GuidelinesCommunityView from "@/modules/community/view/guidelines.view";
import { CommunityHeroView } from "@/modules/community/view/hero.view";
import WhatsAppCommunityView from "@/modules/community/view/whatsapp.view";
import { StrapiCommunityPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";
import { notFound } from "next/navigation";

export default async function CommunityPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/community-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiCommunityPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "community-page.hero-section":
          return <CommunityHeroView key={idx} data={section} />;
        case "community-page.whats-app-section":
          return <WhatsAppCommunityView key={idx} data={section} />;
        case "community-page.discord-section":
          return <DiscordServerCommunityView key={idx} data={section} />;
        case "community-page.guideline-section":
          return <GuidelinesCommunityView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  return <div className="flex flex-1 flex-col">{renderSections()}</div>;
}
