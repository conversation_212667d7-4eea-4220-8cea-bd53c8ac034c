import { strapi } from "@/lib/strapi";
import FaqPricingView from "@/modules/pricing/view/faq.view";
import FeatureComparisonPricingView from "@/modules/pricing/view/feature-comparison.view";
import GuaranteePricingView from "@/modules/pricing/view/guarantee.view";
import HeroPricingView from "@/modules/pricing/view/hero.view";
import PackagePricingView from "@/modules/pricing/view/package.view";
import { StrapiPricingPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";
import { notFound } from "next/navigation";

export default async function PricingPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/pricing-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiPricingPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "pricing-page.hero-section":
          return <HeroPricingView key={idx} data={section} />;
        case "pricing-page.pricing-section":
          return <PackagePricingView key={idx} data={section} />;
        case "pricing-page.guarantee-section":
          return <GuaranteePricingView key={idx} data={section} />;
        case "pricing-page.faq-section":
          return <FaqPricingView key={idx} data={section} />;
        case "pricing-page.feature-section":
          return <FeatureComparisonPricingView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  return <div className="flex flex-1 flex-col">{renderSections()}</div>;
}
