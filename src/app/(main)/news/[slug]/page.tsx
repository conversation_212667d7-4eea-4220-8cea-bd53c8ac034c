import { strapi } from "@/lib/strapi";
import { DetailNewsView } from "@/modules/news/views/detail.view";
import { StrapiNewsArticle, StrapiResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";
import { notFound } from "next/navigation";

export default async function NewsDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const [res, err] = await tryCatch(
    strapi({ path: "/api/news-articles?filters[slug][$eq]=" + slug }),
  );

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiResponse<StrapiNewsArticle[]>;
  const data = response.data[0];

  if (!data) {
    notFound();
  }

  return (
    <div className="bg-muted min-h-screen w-full px-4">
      <DetailNewsView data={data} />
    </div>
  );
}
