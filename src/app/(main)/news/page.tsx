import { strapi } from "@/lib/strapi";
import ArticleNewsView from "@/modules/news/views/artikel.view";
import HeadlinesNewsView from "@/modules/news/views/headlines.view";
import HeroNewsView from "@/modules/news/views/hero.view";
import NewsletterNewsView from "@/modules/news/views/newsletter.view";
import { StrapiNewsPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";
import { notFound } from "next/navigation";

export default async function NewsPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/news-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiNewsPageResponse;
  const data = response.data;
  console.log(data)

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "news-page.hero-section":
          return <HeroNewsView key={idx} data={section} />;
        case "news-page.headline-section":
          return <HeadlinesNewsView key={idx} data={section} />;
        case "news-page.article-section":
          return <ArticleNewsView key={idx} data={section} />;
        case "news-page.newsletter-section":
          return <NewsletterNewsView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  return <div className="flex flex-1 flex-col">{renderSections()}</div>;
}
