import { strapi } from "@/lib/strapi";
import ContactFaqView from "@/modules/faq/views/contact.view";
import FaqSectionView from "@/modules/faq/views/faq-section.view";
import FaqHeroView from "@/modules/faq/views/hero.view";
import { StrapiFaqPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";
import { notFound } from "next/navigation";

export default async function FaqPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/faq-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiFaqPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "faq-page.hero-section":
          return <FaqHeroView key={idx} data={section} />;
        case "faq-page.faq-section":
          return <FaqSectionView key={idx} data={section} />;
        case "faq-page.contact-section":
          return <ContactFaqView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  return <div className="flex flex-1 flex-col">{renderSections()}</div>;
}
