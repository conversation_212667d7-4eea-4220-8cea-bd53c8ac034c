import { notFound } from "next/navigation";

import { strapi } from "@/lib/strapi";
import { HomeCommunityView } from "@/modules/home/<USER>/community.view";
import { HomeCtaView } from "@/modules/home/<USER>/cta.view";
import { HomeFaqView } from "@/modules/home/<USER>/faq.view";
import { HomeFeaturesView } from "@/modules/home/<USER>/features.view";
import { HomeHeroView } from "@/modules/home/<USER>/hero.view";
import { HomeInterfaceView } from "@/modules/home/<USER>/interface.view";
import { HomeJobView } from "@/modules/home/<USER>/job.view";
import { HomePricingView } from "@/modules/home/<USER>/pricing.view";
import { HomeProblemView } from "@/modules/home/<USER>/problem.view";
import { HomeStepsView } from "@/modules/home/<USER>/steps.view";
import { HomeTestimonialsView } from "@/modules/home/<USER>/testimonials.view";
import { StrapiHomePageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";

export default async function HomePage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/home-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiHomePageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "home-page.hero-section":
          return <HomeHeroView key={idx} data={section} />;
        case "home-page.job-section":
          return <HomeJobView key={idx} data={section} />;
        case "home-page.problem-section":
          return <HomeProblemView key={idx} data={section} />;
        case "home-page.tutotial-section":
          return <HomeStepsView key={idx} data={section} />;
        case "home-page.feature-section":
          return <HomeFeaturesView key={idx} data={section} />;
        case "home-page.interface-section":
          return <HomeInterfaceView key={idx} data={section} />;
        case "home-page.community-section":
          return <HomeCommunityView key={idx} data={section} />;
        case "home-page.testimoni-section":
          return <HomeTestimonialsView key={idx} data={section} />;
        case "home-page.pricing-section":
          return <HomePricingView key={idx} data={section} />;
        case "home-page.faq-section":
          return <HomeFaqView key={idx} data={section} />;
        case "home-page.cta-section":
          return <HomeCtaView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  return <div className="flex flex-1 flex-col">{renderSections()}</div>;
}
