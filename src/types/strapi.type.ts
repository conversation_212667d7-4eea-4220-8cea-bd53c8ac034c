export type StrapiResponse<T> = {
  data: T;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
};

export type StrapiMedia = {
  url: string;
  alternativeText: string;
  mime: string;
};

export type StrapiHomeHeroSection = {
  __component: "home-page.hero-section";
  id: number;
  title: string;
  description: string;
  buttonStart: string;
  buttonCommunity: string;
  poster: StrapiMedia | null;
  caption: string;
};

type FloatCompType = {
  id: number;
  title: string;
  count: number;
};

export type StrapiHomeJobSection = {
  __component: "home-page.job-section";
  id: number;
  title: string;
  description: string;
  list: Array<{
    id: number;
    label: string;
  }>;
  poster: StrapiMedia | null;
  float1: FloatCompType;
  float2: FloatCompType;
};

export type StrapiHomeProblemSection = {
  __component: "home-page.problem-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    icon: "eyes" | "question" | "ban";
    bgColor: string;
  }>;
  problemHighlight: {
    title: string;
    description: string;
    cta_text: string;
    cta_link: string;
  };
};

export type StrapiHomeStepsSection = {
  __component: "home-page.tutotial-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    number: number;
    icon: "file" | "briefcase" | "chat" | "chart" | "rocket";
  }>;
};

export type StrapiHomeFeatureSection = {
  __component: "home-page.feature-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    icon: "video" | "chart" | "brain" | "file" | "shield" | "speedo";
  }>;
};

export type StrapiHomeInterfaceSection = {
  __component: "home-page.interface-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    imgAlt: string;
    image: {
      url: string;
      alternativeText: string;
    };
  }>;
};

export type StrapiHomeCommunitySection = {
  __component: "home-page.community-section";
  id: number;
  title: string;
  description: string;
  card: {
    id: number;
    title: string;
    description: string;
    joinWaText: string;
    joinWaLink: string;
    joinDcText: string;
    joinDcLink: string;
    caption: string;
  };
  cards: Array<{
    id: number;
    name: string;
    position: string;
    testimoni: string;
    avatar: StrapiMedia | null;
  }>;
};

export type StrapiHomeTestimoniSection = {
  __component: "home-page.testimoni-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    name: string;
    position: string;
    testimoni: string;
    start: number;
    avatar: StrapiMedia | null;
  }>;
};

export type Benefit = {
  id: number;
  label: string;
  isIncluded: boolean;
};
export type StrapiHomePricingSection = {
  __component: "home-page.pricing-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    buyButton: string;
    period: string;
    isPopuler: boolean;
    discount: string | null;
    price: number;
    benefits: Array<Benefit>;
  }>;
};

export type StrapiHomeFaqSection = {
  __component: "home-page.faq-section";
  id: number;
  title: string;
  description: string;
  Accordion: Array<{
    id: number;
    question: string;
    answer: string;
  }>;
};

export type StrapiHomeCtaSection = {
  __component: "home-page.cta-section";
  id: number;
  title: string;
  description: string;
  startButton: string;
  learnButton: string;
};

export type StrapiHomePageResponse = StrapiResponse<{
  sections: Array<
    | StrapiHomeHeroSection
    | StrapiHomeJobSection
    | StrapiHomeProblemSection
    | StrapiHomeStepsSection
    | StrapiHomeFeatureSection
    | StrapiHomeInterfaceSection
    | StrapiHomeCommunitySection
    | StrapiHomeTestimoniSection
    | StrapiHomePricingSection
    | StrapiHomeFaqSection
    | StrapiHomeCtaSection
  >;
}>;

export type StrapiCommunityHeroSection = {
  __component: "community-page.hero-section";
  id: number;
  title: string;
  description: string;
  buttonWaText: string;
  buttonWaLink: string;
  buttonDcText: string;
  buttonDcLink: string;
  cards: Array<{
    id: number;
    text: string;
    value: string;
    valueColor: string;
    bgColor: string;
  }>;
};

export type StrapiCommunityWhatsappSection = {
  __component: "community-page.whats-app-section";
  id: number;
  title: string;
  subTitle: string;
  description: string;
  joinWaText: string;
  joinWaLink: string;
  list: Array<{
    id: number;
    text: string;
    subText: string;
  }>;
  card: {
    id: number;
    title: string;
    members: number;
  };
  messages: Array<{
    id: number;
    name: string;
    message: string;
    avatar: StrapiMedia | null;
  }>;
};

export type StrapiCommunityDiscordSection = {
  __component: "community-page.discord-section";
  id: number;
  title: string;
  subTitle: string;
  description: string;
  joinDcText: string;
  joinDcLink: string;
  list: Array<{
    id: number;
    text: string;
    subText: string;
  }>;
  listRoomDc: Array<{
    id: number;
    icon: "hash" | "voice";
    text: string;
    isActive: boolean;
  }>;
  cards: {
    id: number;
    title: string;
    members: number;
    name: string;
    avatar: StrapiMedia | null;
  };
};

export type StrapiCommunityGuidelineSection = {
  __component: "community-page.guideline-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    icon: "check" | "heart" | "shield";
    title: string;
    description: string;
    iconBgColor: string;
    cardBgColor: string;
  }>;
};

export type StrapiCommunityPageResponse = StrapiResponse<{
  sections: Array<
    | StrapiCommunityHeroSection
    | StrapiCommunityWhatsappSection
    | StrapiCommunityDiscordSection
    | StrapiCommunityGuidelineSection
  >;
}>;

export type StrapiPricingHeroSection = {
  __component: "pricing-page.hero-section";
  id: number;
  title: string;
  description: string;
  buttonTextMonthly: string;
  buttonTextYearly: string;
};

export type StrapiPricingPlanListItem = {
  id: number;
  label: string;
  isIncluded: boolean;
};

export type StrapiPricingPlan = {
  id: number;
  title: string;
  description: string;
  price: string;
  buttonText: string;
  buttonVariant: string;
  buttonStyle: string;
  cardStyle: string;
  discount: number;
  isPopuler: boolean;
  textColor: string | null;
  lists: StrapiPricingPlanListItem[];
};

export type StrapiPricingSection = {
  __component: "pricing-page.pricing-section";
  id: number;
  plans: StrapiPricingPlan[];
};

export type StrapiPricingFeatureItem = {
  id: number;
  featureName: string;
  freeValue: string;
  proValue: string;
  enterpriseValue: string;
};

export type StrapiPricingFeatureSection = {
  __component: "pricing-page.feature-section";
  id: number;
  title: string;
  description: string;
  features: StrapiPricingFeatureItem[];
};

export type StrapiPricingGuaranteeSection = {
  __component: "pricing-page.guarantee-section";
  id: number;
  title: string;
  description: string;
  buttonText: string;
};

export type StrapiPricingFaqItem = {
  id: number;
  question: string;
  answer: string;
};

export type StrapiPricingFaqSection = {
  __component: "pricing-page.faq-section";
  id: number;
  title: string;
  description: string;
  faqs: StrapiPricingFaqItem[];
};

export type StrapiPricingPageResponse = StrapiResponse<{
  sections: Array<
    | StrapiPricingHeroSection
    | StrapiPricingSection
    | StrapiPricingFeatureSection
    | StrapiPricingGuaranteeSection
    | StrapiPricingFaqSection
  >;
}>;

export type StrapiFaqHeroSection = {
  __component: "faq-page.hero-section";
  id: number;
  title: string;
  description: string;
};

export type StrapiFaqItem = {
  id: number;
  documentId: string;
  question: string;
  answer: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
};

export type StrapiFaqCategory = {
  id: number;
  title: string;
  icon: "info" | "robot" | "search" | "setting";
  faq_items: StrapiFaqItem[];
};

export type StrapiFaqSection = {
  __component: "faq-page.faq-section";
  id: number;
  categories: StrapiFaqCategory[];
};

export type StrapiFaqContactSection = {
  __component: "faq-page.contact-section";
  id: number;
  title: string;
  description: string;
  cards: Array<{
    id: number;
    title: string;
    description: string;
    buttonText: string;
    href: string;
    icon: "chat" | "wa" | "mail";
  }>;
};

export type StrapiFaqPageResponse = StrapiResponse<{
  sections: Array<StrapiFaqHeroSection | StrapiFaqSection | StrapiFaqContactSection>;
}>;

export type StrapiNewsCategory = {
  id: number;
  documentId: string;
  name: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  badgeStyle: string;
  badgeText: string;
};

export type StrapiNewsArticle = {
  id: number;
  documentId: string;
  title: string;
  slug: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  thumbnail: {
    url: string;
    alternativeText: string;
  } | null;
  news_category: StrapiNewsCategory;
};

export type StrapiNewsHeroSection = {
  __component: "news-page.hero-section";
  id: number;
  title: string;
  description: string;
};

export type StrapiNewsHeadlineSection = {
  __component: "news-page.headline-section";
  id: number;
  title: string;
  description: string;
  latest_article: StrapiNewsArticle | null;
  articles: StrapiNewsArticle[] | null;
};

export type StrapiNewsArticleSection = {
  __component: "news-page.article-section";
  id: number;
  title: string;
  description: string;
  articles: StrapiNewsArticle[] | null;
  categories: Array<
    StrapiNewsCategory & {
      news_articles: Array<{
        id: number;
        documentId: string;
        title: string;
        slug: string;
        content: string;
        createdAt: string;
        updatedAt: string;
        publishedAt: string;
      }>;
    }
  > | null;
};

export type StrapiNewsNewsletterSection = {
  __component: "news-page.newsletter-section";
  id: number;
  title: string;
  description: string;
  caption: string;
};

export type StrapiNewsPageResponse = StrapiResponse<{
  sections: Array<
    | StrapiNewsHeroSection
    | StrapiNewsHeadlineSection
    | StrapiNewsArticleSection
    | StrapiNewsNewsletterSection
  >;
}>;
