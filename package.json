{"name": "next-ai-interview", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.14.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "shadcn:add": "pnpm dlx shadcn@latest add"}, "dependencies": {"@heygen/streaming-avatar": "^2.0.16", "@livekit/components-react": "^2.9.13", "@origin-space/image-cropper": "^0.1.9", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.51.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.81.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.0.0", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "livekit-client": "^2.15.2", "lucide-react": "^0.518.0", "motion": "^12.18.1", "nanoid": "^5.1.5", "next": "15.3.4", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-phone-number-input": "^3.4.12", "react-syntax-highlighter": "^15.6.1", "react-use-measure": "^2.1.7", "recharts": "2.15.4", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "tailwind-clamp": "^4.0.18", "tailwind-merge": "^3.3.1", "ws": "^8.18.3", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}